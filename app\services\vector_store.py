"""ChromaDB vector store service for document embedding and retrieval."""

import chromadb
from chromadb.config import Settings
import logging
from typing import List, Dict, Any, Optional, Tuple
import hashlib
import json
from pathlib import Path

from app.models.schemas import ReportData, Question
from app.config import settings

logger = logging.getLogger(__name__)


class VectorStore:
    """Service for managing document embeddings and retrieval using ChromaDB."""
    
    def __init__(self, db_path: Optional[str] = None, collection_name: Optional[str] = None):
        """
        Initialize the vector store.
        
        Args:
            db_path: Path to ChromaDB database (defaults to config)
            collection_name: Name of the collection (defaults to config)
        """
        self.db_path = db_path or settings.chroma_db_path
        self.collection_name = collection_name or settings.chroma_collection_name
        self.client = None
        self.collection = None
        
        # Ensure database directory exists
        Path(self.db_path).mkdir(parents=True, exist_ok=True)
        
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize ChromaDB client and collection."""
        try:
            # Initialize ChromaDB client with persistent storage
            self.client = chromadb.PersistentClient(
                path=self.db_path,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Get or create collection
            self.collection = self.client.get_or_create_collection(
                name=self.collection_name,
                metadata={"description": "AI Report Validator document embeddings"}
            )
            
            logger.info(f"ChromaDB initialized at {self.db_path}, collection: {self.collection_name}")
            
        except Exception as e:
            logger.error(f"Failed to initialize ChromaDB: {str(e)}")
            raise
    
    def add_report(self, report_data: ReportData, text_content: str) -> str:
        """
        Add a report to the vector store.
        
        Args:
            report_data: The report data object
            text_content: Extracted text content from the report
            
        Returns:
            Document ID in the vector store
        """
        try:
            # Create document ID
            doc_id = f"report_{report_data.report_id}"
            
            # Prepare metadata
            metadata = {
                "type": "report",
                "report_id": report_data.report_id,
                "source_url": report_data.source_url or "",
                "fetched_at": report_data.fetched_at.isoformat(),
                "content_length": len(text_content)
            }
            
            # Add additional metadata if available
            if report_data.metadata:
                for key, value in report_data.metadata.items():
                    if isinstance(value, (str, int, float, bool)):
                        metadata[f"meta_{key}"] = value
            
            # Add document to collection
            self.collection.add(
                documents=[text_content],
                metadatas=[metadata],
                ids=[doc_id]
            )
            
            logger.info(f"Added report {report_data.report_id} to vector store")
            return doc_id
            
        except Exception as e:
            logger.error(f"Failed to add report {report_data.report_id} to vector store: {str(e)}")
            raise
    
    def add_question_context(self, questions: List[Question], context_name: str) -> List[str]:
        """
        Add questions as context documents to the vector store.
        
        Args:
            questions: List of questions to add
            context_name: Name identifier for this question set
            
        Returns:
            List of document IDs in the vector store
        """
        try:
            doc_ids = []
            documents = []
            metadatas = []
            
            for question in questions:
                # Create document ID
                doc_id = f"question_{context_name}_{question.id}"
                
                # Prepare document text
                doc_text = f"Question: {question.text}"
                if question.category:
                    doc_text += f"\nCategory: {question.category}"
                if question.priority:
                    doc_text += f"\nPriority: {question.priority}"
                if question.expected_answer_type:
                    doc_text += f"\nExpected Answer Type: {question.expected_answer_type}"
                
                # Prepare metadata
                metadata = {
                    "type": "question",
                    "question_id": question.id,
                    "context_name": context_name,
                    "category": question.category or "",
                    "priority": question.priority or "",
                    "expected_answer_type": question.expected_answer_type or ""
                }
                
                # Add question metadata if available
                if question.metadata:
                    for key, value in question.metadata.items():
                        if isinstance(value, (str, int, float, bool)):
                            metadata[f"meta_{key}"] = value
                
                doc_ids.append(doc_id)
                documents.append(doc_text)
                metadatas.append(metadata)
            
            # Add all documents to collection
            self.collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=doc_ids
            )
            
            logger.info(f"Added {len(questions)} questions from {context_name} to vector store")
            return doc_ids
            
        except Exception as e:
            logger.error(f"Failed to add questions from {context_name} to vector store: {str(e)}")
            raise
    
    def search_relevant_content(
        self, 
        query: str, 
        n_results: int = 5,
        filter_metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for relevant content in the vector store.
        
        Args:
            query: Search query text
            n_results: Number of results to return
            filter_metadata: Optional metadata filters
            
        Returns:
            List of relevant documents with metadata and distances
        """
        try:
            # Perform similarity search
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results,
                where=filter_metadata
            )
            
            # Format results
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i in range(len(results['documents'][0])):
                    result = {
                        'document': results['documents'][0][i],
                        'metadata': results['metadatas'][0][i],
                        'distance': results['distances'][0][i] if results['distances'] else None,
                        'id': results['ids'][0][i]
                    }
                    formatted_results.append(result)
            
            logger.info(f"Found {len(formatted_results)} relevant documents for query")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to search vector store: {str(e)}")
            raise
    
    def get_report_content(self, report_id: str) -> Optional[Dict[str, Any]]:
        """
        Get stored content for a specific report.
        
        Args:
            report_id: ID of the report to retrieve
            
        Returns:
            Document data or None if not found
        """
        try:
            doc_id = f"report_{report_id}"
            
            results = self.collection.get(
                ids=[doc_id],
                include=['documents', 'metadatas']
            )
            
            if results['documents'] and results['documents'][0]:
                return {
                    'document': results['documents'][0],
                    'metadata': results['metadatas'][0],
                    'id': results['ids'][0]
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get report {report_id} from vector store: {str(e)}")
            return None
    
    def delete_report(self, report_id: str) -> bool:
        """
        Delete a report from the vector store.
        
        Args:
            report_id: ID of the report to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            doc_id = f"report_{report_id}"
            self.collection.delete(ids=[doc_id])
            logger.info(f"Deleted report {report_id} from vector store")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete report {report_id} from vector store: {str(e)}")
            return False
    
    def delete_question_context(self, context_name: str) -> bool:
        """
        Delete all questions from a specific context.
        
        Args:
            context_name: Name of the question context to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Find all question documents for this context
            results = self.collection.get(
                where={"type": "question", "context_name": context_name},
                include=['ids']
            )
            
            if results['ids']:
                self.collection.delete(ids=results['ids'])
                logger.info(f"Deleted {len(results['ids'])} questions from context {context_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete question context {context_name}: {str(e)}")
            return False
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the vector store collection.
        
        Returns:
            Dict containing collection statistics
        """
        try:
            # Get all documents with metadata
            results = self.collection.get(include=['metadatas'])
            
            total_docs = len(results['ids']) if results['ids'] else 0
            
            # Count by type
            type_counts = {}
            for metadata in results['metadatas'] or []:
                doc_type = metadata.get('type', 'unknown')
                type_counts[doc_type] = type_counts.get(doc_type, 0) + 1
            
            return {
                "total_documents": total_docs,
                "document_types": type_counts,
                "collection_name": self.collection_name,
                "database_path": self.db_path
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {str(e)}")
            return {"error": str(e)}
    
    def reset_collection(self) -> bool:
        """
        Reset the entire collection (delete all documents).
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Delete the collection and recreate it
            self.client.delete_collection(name=self.collection_name)
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "AI Report Validator document embeddings"}
            )
            
            logger.info(f"Reset collection {self.collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to reset collection: {str(e)}")
            return False
