"""RAG pipeline using LangChain and OpenAI for report validation."""

import logging
from typing import List, Dict, Any, Optional, Tuple
import time
import asyncio
from datetime import datetime

from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain.schema import BaseMessage
from langchain.schema.output_parser import StrOutputParser

from app.models.schemas import (
    Question, ReportData, ValidationItem, ValidationResult
)
from app.services.vector_store import VectorStore
from app.config import settings

logger = logging.getLogger(__name__)


class RAGPipeline:
    """RAG pipeline for validating reports against questions using LangChain and OpenAI."""
    
    def __init__(self, vector_store: Optional[VectorStore] = None):
        """
        Initialize the RAG pipeline.
        
        Args:
            vector_store: Vector store instance (creates new if None)
        """
        self.vector_store = vector_store or VectorStore()
        self.llm = None
        self._initialize_llm()
        self._setup_prompts()
    
    def _initialize_llm(self):
        """Initialize the OpenAI language model."""
        try:
            if not settings.openai_api_key:
                raise ValueError("OpenAI API key not configured")
            
            self.llm = ChatOpenAI(
                model=settings.openai_model,
                api_key=settings.openai_api_key,
                temperature=0.1,  # Low temperature for consistent results
                max_tokens=1000,
                timeout=30
            )
            
            logger.info(f"Initialized OpenAI LLM with model: {settings.openai_model}")
            
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI LLM: {str(e)}")
            raise
    
    def _setup_prompts(self):
        """Setup prompt templates for validation."""
        
        # Main validation prompt
        self.validation_prompt = ChatPromptTemplate.from_messages([
            ("system", """You are an expert report validator. Your task is to validate whether a report adequately addresses a specific question based on relevant context from the report.

Instructions:
1. Analyze the question and understand what information is being requested
2. Review the relevant report sections provided as context
3. Determine if the report contains sufficient information to answer the question
4. Provide a validation result with confidence score and explanation

Validation Results:
- PASS: The report clearly and adequately addresses the question
- FAIL: The report does not address the question or provides insufficient information
- PARTIAL: The report partially addresses the question but lacks some important details
- INCONCLUSIVE: Cannot determine if the question is addressed due to ambiguous or unclear information

Provide your response in the following format:
RESULT: [PASS/FAIL/PARTIAL/INCONCLUSIVE]
CONFIDENCE: [0.0-1.0]
EXPLANATION: [Detailed explanation of your reasoning]
RELEVANT_SECTIONS: [List the most relevant sections that influenced your decision]"""),
            
            ("human", """Question to validate: {question}

Relevant report context:
{context}

Please validate whether the report adequately addresses this question.""")
        ])
        
        # Context extraction prompt for better retrieval
        self.context_prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a document analysis expert. Extract key terms and concepts from the given question that would be important for finding relevant information in a report."""),
            ("human", """Question: {question}

Extract the most important keywords, concepts, and terms that should be searched for in a report to answer this question. Return them as a comma-separated list.""")
        ])
    
    async def validate_question_against_report(
        self, 
        question: Question, 
        report_id: str,
        max_context_length: int = 3000
    ) -> ValidationItem:
        """
        Validate a single question against a report using RAG.
        
        Args:
            question: The question to validate
            report_id: ID of the report to validate against
            max_context_length: Maximum length of context to include
            
        Returns:
            ValidationItem with validation results
        """
        start_time = time.time()
        
        try:
            # Extract key terms from the question for better retrieval
            search_terms = await self._extract_search_terms(question.text)
            
            # Search for relevant content in the vector store
            relevant_docs = self.vector_store.search_relevant_content(
                query=f"{question.text} {search_terms}",
                n_results=5,
                filter_metadata={"type": "report", "report_id": report_id}
            )
            
            if not relevant_docs:
                # Fallback: search without filters
                relevant_docs = self.vector_store.search_relevant_content(
                    query=question.text,
                    n_results=3
                )
            
            # Prepare context from relevant documents
            context = self._prepare_context(relevant_docs, max_context_length)
            
            # Validate using the LLM
            validation_result = await self._perform_validation(question.text, context)
            
            # Calculate processing time
            processing_time = int((time.time() - start_time) * 1000)
            
            # Extract relevant sections
            relevant_sections = self._extract_relevant_sections(relevant_docs)
            
            return ValidationItem(
                question_id=question.id,
                question_text=question.text,
                result=validation_result["result"],
                confidence_score=validation_result["confidence"],
                explanation=validation_result["explanation"],
                relevant_report_sections=relevant_sections,
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            logger.error(f"Error validating question {question.id}: {str(e)}")
            
            processing_time = int((time.time() - start_time) * 1000)
            
            return ValidationItem(
                question_id=question.id,
                question_text=question.text,
                result=ValidationResult.INCONCLUSIVE,
                confidence_score=0.0,
                explanation=f"Validation failed due to error: {str(e)}",
                relevant_report_sections=[],
                processing_time_ms=processing_time
            )
    
    async def _extract_search_terms(self, question: str) -> str:
        """Extract key search terms from a question."""
        try:
            chain = self.context_prompt | self.llm | StrOutputParser()
            search_terms = await chain.ainvoke({"question": question})
            return search_terms.strip()
        except Exception as e:
            logger.warning(f"Failed to extract search terms: {str(e)}")
            return question  # Fallback to original question
    
    def _prepare_context(self, relevant_docs: List[Dict[str, Any]], max_length: int) -> str:
        """Prepare context from relevant documents."""
        context_parts = []
        current_length = 0
        
        for doc in relevant_docs:
            doc_text = doc['document']
            doc_metadata = doc.get('metadata', {})
            
            # Add section header
            section_header = f"\n--- Report Section ---\n"
            if doc_metadata.get('report_id'):
                section_header = f"\n--- Report {doc_metadata['report_id']} Section ---\n"
            
            section_text = section_header + doc_text
            
            # Check if adding this section would exceed max length
            if current_length + len(section_text) > max_length:
                # Truncate the section to fit
                remaining_space = max_length - current_length - len(section_header)
                if remaining_space > 100:  # Only add if there's meaningful space
                    truncated_text = doc_text[:remaining_space] + "..."
                    context_parts.append(section_header + truncated_text)
                break
            
            context_parts.append(section_text)
            current_length += len(section_text)
        
        return "\n".join(context_parts)
    
    async def _perform_validation(self, question: str, context: str) -> Dict[str, Any]:
        """Perform the actual validation using the LLM."""
        try:
            chain = self.validation_prompt | self.llm | StrOutputParser()
            
            response = await chain.ainvoke({
                "question": question,
                "context": context
            })
            
            # Parse the structured response
            return self._parse_validation_response(response)
            
        except Exception as e:
            logger.error(f"LLM validation failed: {str(e)}")
            raise
    
    def _parse_validation_response(self, response: str) -> Dict[str, Any]:
        """Parse the structured validation response from the LLM."""
        try:
            lines = response.strip().split('\n')
            result_data = {
                "result": ValidationResult.INCONCLUSIVE,
                "confidence": 0.0,
                "explanation": "Failed to parse response",
                "relevant_sections": []
            }
            
            for line in lines:
                line = line.strip()
                if line.startswith("RESULT:"):
                    result_str = line.replace("RESULT:", "").strip().upper()
                    if result_str in [r.value.upper() for r in ValidationResult]:
                        result_data["result"] = ValidationResult(result_str.lower())
                
                elif line.startswith("CONFIDENCE:"):
                    try:
                        confidence_str = line.replace("CONFIDENCE:", "").strip()
                        result_data["confidence"] = float(confidence_str)
                    except ValueError:
                        pass
                
                elif line.startswith("EXPLANATION:"):
                    explanation = line.replace("EXPLANATION:", "").strip()
                    # Collect multi-line explanations
                    explanation_lines = [explanation]
                    for next_line in lines[lines.index(line) + 1:]:
                        if next_line.strip().startswith(("RELEVANT_SECTIONS:", "RESULT:", "CONFIDENCE:")):
                            break
                        explanation_lines.append(next_line.strip())
                    result_data["explanation"] = " ".join(explanation_lines).strip()
                
                elif line.startswith("RELEVANT_SECTIONS:"):
                    sections_str = line.replace("RELEVANT_SECTIONS:", "").strip()
                    if sections_str:
                        result_data["relevant_sections"] = [s.strip() for s in sections_str.split(",")]
            
            return result_data
            
        except Exception as e:
            logger.error(f"Failed to parse validation response: {str(e)}")
            return {
                "result": ValidationResult.INCONCLUSIVE,
                "confidence": 0.0,
                "explanation": f"Response parsing failed: {str(e)}",
                "relevant_sections": []
            }
    
    def _extract_relevant_sections(self, relevant_docs: List[Dict[str, Any]]) -> List[str]:
        """Extract section identifiers from relevant documents."""
        sections = []
        for doc in relevant_docs:
            metadata = doc.get('metadata', {})
            if metadata.get('report_id'):
                section_id = f"Report {metadata['report_id']}"
                if section_id not in sections:
                    sections.append(section_id)
        
        return sections[:5]  # Limit to top 5 sections
    
    async def validate_multiple_questions(
        self, 
        questions: List[Question], 
        report_id: str
    ) -> List[ValidationItem]:
        """
        Validate multiple questions against a report.
        
        Args:
            questions: List of questions to validate
            report_id: ID of the report to validate against
            
        Returns:
            List of ValidationItem results
        """
        results = []
        
        for question in questions:
            try:
                result = await self.validate_question_against_report(question, report_id)
                results.append(result)
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Failed to validate question {question.id}: {str(e)}")
                # Add error result
                results.append(ValidationItem(
                    question_id=question.id,
                    question_text=question.text,
                    result=ValidationResult.INCONCLUSIVE,
                    confidence_score=0.0,
                    explanation=f"Validation error: {str(e)}",
                    relevant_report_sections=[],
                    processing_time_ms=0
                ))
        
        return results
