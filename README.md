# AI Report Validator

An AI-powered report validation system that uses RAG (Retrieval-Augmented Generation) and LLM technology to validate XML reports against questions from Excel files.

## Features

- **Excel Question Upload**: Upload Excel files containing validation questions
- **XML Report Fetching**: Fetch XML reports from external APIs
- **RAG-based Validation**: Use ChromaDB vector database and OpenAI GPT-4o-mini for intelligent validation
- **FastAPI REST API**: Complete REST API for all operations
- **Background Processing**: Asynchronous validation processing
- **Detailed Results**: Comprehensive validation reports with confidence scores and explanations

## Technology Stack

- **FastAPI**: Modern web framework for building APIs
- **Lang<PERSON>hain**: Framework for building LLM applications
- **OpenAI GPT-4o-mini**: Language model for validation logic
- **ChromaDB**: Vector database for RAG implementation
- **Pandas & OpenPyXL**: Excel file processing
- **XMLtoDict**: XML parsing and processing
- **Pydantic**: Data validation and settings management

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd icpcredit-ai
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and configure:
   ```env
   OPENAI_API_KEY=your_openai_api_key_here
   EXTERNAL_API_BASE_URL=https://your-external-api.com
   EXTERNAL_API_KEY=your_external_api_key_here
   ```

## Usage

### 1. Start the Application

```bash
python main.py
```

The API will be available at `http://localhost:8000`

### 2. API Documentation

Visit `http://localhost:8000/docs` for interactive API documentation.

### 3. Basic Workflow

#### Step 1: Upload Questions
```bash
curl -X POST "http://localhost:8000/api/v1/questions/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@questions.xlsx"
```

#### Step 2: Fetch Report
```bash
curl -X POST "http://localhost:8000/api/v1/reports/fetch/REPORT_ID"
```

#### Step 3: Start Validation
```bash
curl -X POST "http://localhost:8000/api/v1/validation/start" \
  -H "Content-Type: application/json" \
  -d '{
    "report_id": "REPORT_ID",
    "question_set_filename": "questions.xlsx"
  }'
```

#### Step 4: Get Results
```bash
curl "http://localhost:8000/api/v1/validation/VALIDATION_ID"
```

## Excel File Format

Your Excel file should contain questions with the following columns:

| Column | Required | Description |
|--------|----------|-------------|
| question/text | Yes | The validation question |
| category | No | Question category |
| priority | No | Question priority |
| expected_answer_type | No | Expected answer format |

Example:
```
| question | category | priority |
|----------|----------|----------|
| Does the report include financial data? | Financial | High |
| Are risk factors clearly identified? | Risk | Medium |
```

## API Endpoints

### Questions
- `POST /api/v1/questions/upload` - Upload Excel file with questions
- `GET /api/v1/questions/list` - List all question sets
- `GET /api/v1/questions/{context_name}` - Get specific question set
- `DELETE /api/v1/questions/{context_name}` - Delete question set

### Reports
- `POST /api/v1/reports/fetch/{report_id}` - Fetch report from external API
- `GET /api/v1/reports/{report_id}` - Get stored report
- `GET /api/v1/reports/` - List all reports
- `DELETE /api/v1/reports/{report_id}` - Delete report
- `POST /api/v1/reports/search` - Search reports

### Validation
- `POST /api/v1/validation/start` - Start validation process
- `GET /api/v1/validation/{validation_id}` - Get validation results
- `GET /api/v1/validation/{validation_id}/status` - Get validation status
- `GET /api/v1/validation/` - List all validations
- `DELETE /api/v1/validation/{validation_id}` - Delete validation

## Configuration

Key configuration options in `.env`:

```env
# OpenAI Configuration
OPENAI_API_KEY=your_key_here
OPENAI_MODEL=gpt-4o-mini

# External API
EXTERNAL_API_BASE_URL=https://api.example.com
EXTERNAL_API_KEY=your_api_key

# ChromaDB
CHROMA_DB_PATH=./chroma_db
CHROMA_COLLECTION_NAME=report_validation

# Application
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=True

# File Upload
MAX_FILE_SIZE_MB=10
ALLOWED_EXCEL_EXTENSIONS=.xlsx,.xls
```

## Development

### Project Structure
```
app/
├── __init__.py
├── main.py              # FastAPI application
├── config.py            # Configuration settings
├── models/
│   └── schemas.py       # Pydantic models
├── services/
│   ├── excel_reader.py  # Excel processing
│   ├── report_fetcher.py # XML report fetching
│   ├── vector_store.py  # ChromaDB operations
│   ├── rag_pipeline.py  # RAG validation logic
│   └── validation_service.py # Validation coordination
└── api/
    └── routes/
        ├── questions.py # Question management
        ├── reports.py   # Report management
        └── validation.py # Validation endpoints
```

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest
```

## Troubleshooting

### Common Issues

1. **OpenAI API Key Error**
   - Ensure `OPENAI_API_KEY` is set in `.env`
   - Verify the API key is valid and has sufficient credits

2. **ChromaDB Issues**
   - Check that `CHROMA_DB_PATH` directory is writable
   - Delete `./chroma_db` folder to reset the database

3. **Excel File Upload Errors**
   - Ensure Excel file has required columns
   - Check file size is under the limit
   - Verify file format is `.xlsx` or `.xls`

4. **External API Connection**
   - Verify `EXTERNAL_API_BASE_URL` is correct
   - Check API key and authentication
   - Ensure network connectivity

### Logs

Application logs are written to stdout. Set `DEBUG=True` in `.env` for detailed logging.

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]
