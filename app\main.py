"""Main FastAPI application for AI Report Validator."""

import logging
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from app.config import settings
from app.api.routes import questions, reports, validation
from app.services.vector_store import VectorStore

# Configure logging
logging.basicConfig(
    level=logging.INFO if not settings.debug else logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Global vector store instance
vector_store = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global vector_store
    
    # Startup
    logger.info("Starting AI Report Validator application")
    try:
        # Initialize vector store
        vector_store = VectorStore()
        app.state.vector_store = vector_store
        logger.info("Vector store initialized successfully")
        
        # Log configuration
        logger.info(f"OpenAI Model: {settings.openai_model}")
        logger.info(f"ChromaDB Path: {settings.chroma_db_path}")
        logger.info(f"Debug Mode: {settings.debug}")
        
    except Exception as e:
        logger.error(f"Failed to initialize application: {str(e)}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down AI Report Validator application")


# Create FastAPI application
app = FastAPI(
    title="AI Report Validator",
    description="AI-powered report validation system using RAG and LLM",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for unhandled errors."""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": str(exc) if settings.debug else "An unexpected error occurred"
        }
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check vector store
        stats = app.state.vector_store.get_collection_stats()
        
        return {
            "status": "healthy",
            "version": "1.0.0",
            "vector_store": {
                "status": "connected",
                "total_documents": stats.get("total_documents", 0)
            },
            "configuration": {
                "openai_model": settings.openai_model,
                "debug_mode": settings.debug
            }
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e)
            }
        )


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "AI Report Validator API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }


# Include API routes
app.include_router(questions.router, prefix="/api/v1/questions", tags=["Questions"])
app.include_router(reports.router, prefix="/api/v1/reports", tags=["Reports"])
app.include_router(validation.router, prefix="/api/v1/validation", tags=["Validation"])


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.app_host,
        port=settings.app_port,
        reload=settings.debug
    )
