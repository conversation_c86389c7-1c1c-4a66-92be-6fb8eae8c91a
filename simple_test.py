"""Simple test without ChromaDB to verify basic functionality."""

import asyncio
import tempfile
import os
import pandas as pd
from pathlib import Path

# Test basic imports
try:
    from app.services.excel_reader import ExcelQuestionReader
    from app.services.report_fetcher import ReportFetcher
    from app.models.schemas import Question, ReportData
    from app.config import settings
    print("✓ All imports successful")
except ImportError as e:
    print(f"✗ Import error: {e}")
    exit(1)


def test_excel_reader():
    """Test Excel question reading functionality."""
    print("\nTesting Excel Reader...")
    
    # Create a sample Excel file
    sample_data = {
        'question': [
            'Does the report include financial statements?',
            'Are risk factors clearly identified?',
            'Is the management discussion complete?'
        ],
        'category': ['Financial', 'Risk', 'Management'],
        'priority': ['High', 'Medium', 'High']
    }
    
    df = pd.DataFrame(sample_data)
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
        df.to_excel(temp_file.name, index=False)
        temp_file_path = temp_file.name
    
    try:
        # Test reading the Excel file
        reader = ExcelQuestionReader()
        question_set = reader.read_questions_from_file(temp_file_path)
        
        print(f"✓ Successfully read {len(question_set.questions)} questions")
        print(f"  - Filename: {question_set.filename}")
        print(f"  - Total questions: {question_set.total_questions}")
        
        for i, question in enumerate(question_set.questions[:2]):
            print(f"  - Question {i+1}: {question.text[:50]}...")
            print(f"    Category: {question.category}, Priority: {question.priority}")
        
        return True
        
    except Exception as e:
        print(f"✗ Excel reader test failed: {str(e)}")
        return False
    
    finally:
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)


def test_xml_validation():
    """Test XML content validation."""
    print("\nTesting XML Validation...")
    
    try:
        # Sample XML content
        sample_xml = """<?xml version="1.0" encoding="UTF-8"?>
        <report>
            <header>
                <title>Financial Report 2024</title>
                <date>2024-01-01</date>
            </header>
            <content>
                <section name="financial">
                    <revenue>1000000</revenue>
                    <expenses>800000</expenses>
                    <profit>200000</profit>
                </section>
                <section name="risks">
                    <risk type="market">Market volatility may impact results</risk>
                    <risk type="credit">Credit risk from customer defaults</risk>
                </section>
            </content>
        </report>"""
        
        # Test XML validation
        fetcher = ReportFetcher()
        validation_result = fetcher.validate_xml_content(sample_xml)
        
        if validation_result['valid']:
            print("✓ XML validation successful")
            print(f"  - Root keys: {validation_result['root_keys']}")
            print(f"  - Content length: {validation_result['content_length']}")
        else:
            print(f"✗ XML validation failed: {validation_result['error']}")
            return False
        
        # Test text extraction
        from datetime import datetime
        
        report_data = ReportData(
            report_id="test_report",
            content=fetcher._parse_xml_content(sample_xml),
            fetched_at=datetime.now()
        )
        
        extracted_text = fetcher.extract_text_content(report_data)
        print(f"✓ Extracted {len(extracted_text)} characters of text")
        print(f"  - Text preview: {extracted_text[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ XML validation test failed: {str(e)}")
        return False


def test_configuration():
    """Test application configuration."""
    print("\nTesting Configuration...")
    
    try:
        print(f"✓ Configuration loaded")
        print(f"  - OpenAI Model: {settings.openai_model}")
        print(f"  - ChromaDB Path: {settings.chroma_db_path}")
        print(f"  - Debug Mode: {settings.debug}")
        print(f"  - Max File Size: {settings.max_file_size_mb}MB")
        
        # Check if OpenAI API key is configured
        if settings.openai_api_key:
            print("✓ OpenAI API key is configured")
        else:
            print("⚠ OpenAI API key not configured (set OPENAI_API_KEY in .env)")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {str(e)}")
        return False


def test_fastapi_imports():
    """Test FastAPI related imports."""
    print("\nTesting FastAPI Imports...")
    
    try:
        from app.main import app
        from app.api.routes import questions, reports, validation
        print("✓ FastAPI application imports successful")
        return True
        
    except Exception as e:
        print(f"✗ FastAPI imports failed: {str(e)}")
        return False


def run_all_tests():
    """Run all tests."""
    print("AI Report Validator - Simple Test Suite")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Excel Reader", test_excel_reader),
        ("XML Validation", test_xml_validation),
        ("FastAPI Imports", test_fastapi_imports)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("🎉 All basic tests passed!")
        print("\nNext steps:")
        print("1. Set up your .env file with API keys")
        print("2. Install ChromaDB (may require resolving version conflicts)")
        print("3. Run the full application with: python main.py")
    else:
        print("⚠ Some tests failed. Please check the issues above.")
    
    return passed == len(results)


if __name__ == "__main__":
    run_all_tests()
