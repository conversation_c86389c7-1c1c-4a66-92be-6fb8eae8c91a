"""Service for fetching and parsing XML reports from external APIs."""

import httpx
import xmltodict
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import asyncio

from app.models.schemas import ReportData
from app.config import settings

logger = logging.getLogger(__name__)


class ReportFetcher:
    """Service to fetch and parse XML reports from external APIs."""
    
    def __init__(self, base_url: Optional[str] = None, api_key: Optional[str] = None):
        """
        Initialize the report fetcher.
        
        Args:
            base_url: Base URL for the external API (defaults to config)
            api_key: API key for authentication (defaults to config)
        """
        self.base_url = base_url or settings.external_api_base_url
        self.api_key = api_key or settings.external_api_key
        self.timeout = 30.0  # Default timeout in seconds
        
        if not self.base_url:
            logger.warning("No external API base URL configured")
    
    async def fetch_report(self, report_id: str, endpoint: Optional[str] = None) -> ReportData:
        """
        Fetch a report from the external API and parse it.
        
        Args:
            report_id: Unique identifier for the report
            endpoint: Optional custom endpoint (defaults to /reports/{report_id})
            
        Returns:
            ReportData: Parsed report data
            
        Raises:
            ValueError: If report_id is invalid or API configuration is missing
            httpx.HTTPError: If API request fails
            Exception: If XML parsing fails
        """
        if not report_id or not report_id.strip():
            raise ValueError("Report ID cannot be empty")
        
        if not self.base_url:
            raise ValueError("External API base URL not configured")
        
        # Construct the API URL
        if endpoint:
            url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        else:
            url = f"{self.base_url.rstrip('/')}/reports/{report_id}"
        
        # Prepare headers
        headers = {
            "Accept": "application/xml, text/xml",
            "User-Agent": "AI-Report-Validator/1.0"
        }
        
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                logger.info(f"Fetching report {report_id} from {url}")
                
                response = await client.get(url, headers=headers)
                response.raise_for_status()
                
                # Check content type
                content_type = response.headers.get("content-type", "").lower()
                if "xml" not in content_type:
                    logger.warning(f"Unexpected content type: {content_type}")
                
                # Parse XML content
                xml_content = response.text
                parsed_content = self._parse_xml_content(xml_content)
                
                return ReportData(
                    report_id=report_id,
                    content=parsed_content,
                    source_url=url,
                    fetched_at=datetime.now(),
                    metadata={
                        "content_type": content_type,
                        "content_length": len(xml_content),
                        "status_code": response.status_code,
                        "response_headers": dict(response.headers)
                    }
                )
                
        except httpx.HTTPError as e:
            logger.error(f"HTTP error fetching report {report_id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error fetching report {report_id}: {str(e)}")
            raise
    
    def fetch_report_sync(self, report_id: str, endpoint: Optional[str] = None) -> ReportData:
        """
        Synchronous version of fetch_report.
        
        Args:
            report_id: Unique identifier for the report
            endpoint: Optional custom endpoint
            
        Returns:
            ReportData: Parsed report data
        """
        return asyncio.run(self.fetch_report(report_id, endpoint))
    
    def _parse_xml_content(self, xml_content: str) -> Dict[str, Any]:
        """
        Parse XML content into a dictionary.
        
        Args:
            xml_content: Raw XML content as string
            
        Returns:
            Dict containing parsed XML data
            
        Raises:
            Exception: If XML parsing fails
        """
        try:
            # Parse XML to dictionary
            parsed_data = xmltodict.parse(xml_content)
            
            # Log parsing success
            logger.info(f"Successfully parsed XML content ({len(xml_content)} characters)")
            
            return parsed_data
            
        except Exception as e:
            logger.error(f"Failed to parse XML content: {str(e)}")
            logger.debug(f"XML content preview: {xml_content[:500]}...")
            raise Exception(f"XML parsing failed: {str(e)}")
    
    async def fetch_multiple_reports(
        self, 
        report_ids: list[str], 
        max_concurrent: int = 5
    ) -> Dict[str, ReportData]:
        """
        Fetch multiple reports concurrently.
        
        Args:
            report_ids: List of report IDs to fetch
            max_concurrent: Maximum number of concurrent requests
            
        Returns:
            Dict mapping report IDs to ReportData objects
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        results = {}
        
        async def fetch_single(report_id: str):
            async with semaphore:
                try:
                    report_data = await self.fetch_report(report_id)
                    results[report_id] = report_data
                except Exception as e:
                    logger.error(f"Failed to fetch report {report_id}: {str(e)}")
                    results[report_id] = None
        
        # Create tasks for all reports
        tasks = [fetch_single(report_id) for report_id in report_ids]
        
        # Wait for all tasks to complete
        await asyncio.gather(*tasks, return_exceptions=True)
        
        return results
    
    def validate_xml_content(self, xml_content: str) -> Dict[str, Any]:
        """
        Validate XML content and return information about its structure.
        
        Args:
            xml_content: Raw XML content as string
            
        Returns:
            Dict containing validation information
        """
        try:
            parsed_data = xmltodict.parse(xml_content)
            
            # Analyze structure
            root_keys = list(parsed_data.keys()) if isinstance(parsed_data, dict) else []
            
            def count_elements(data, depth=0, max_depth=3):
                """Recursively count elements in the parsed data."""
                if depth > max_depth:
                    return {"truncated": True}
                
                if isinstance(data, dict):
                    return {
                        "type": "dict",
                        "keys": list(data.keys())[:10],  # Limit to first 10 keys
                        "total_keys": len(data.keys()),
                        "children": {k: count_elements(v, depth + 1, max_depth) 
                                   for k, v in list(data.items())[:5]}  # Limit to first 5 children
                    }
                elif isinstance(data, list):
                    return {
                        "type": "list",
                        "length": len(data),
                        "sample_items": [count_elements(item, depth + 1, max_depth) 
                                       for item in data[:3]]  # Sample first 3 items
                    }
                else:
                    return {
                        "type": type(data).__name__,
                        "value_preview": str(data)[:100] if data else None
                    }
            
            structure_info = count_elements(parsed_data)
            
            return {
                "valid": True,
                "root_keys": root_keys,
                "structure": structure_info,
                "content_length": len(xml_content),
                "estimated_elements": len(str(parsed_data))
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": str(e),
                "content_preview": xml_content[:200] if xml_content else None
            }
    
    def extract_text_content(self, report_data: ReportData) -> str:
        """
        Extract all text content from a parsed report for indexing.
        
        Args:
            report_data: The parsed report data
            
        Returns:
            String containing all extractable text content
        """
        def extract_text_recursive(data, texts=None):
            """Recursively extract text from nested data structures."""
            if texts is None:
                texts = []
            
            if isinstance(data, dict):
                for key, value in data.items():
                    # Add key as context
                    if isinstance(key, str) and key.strip():
                        texts.append(f"[{key}]")
                    extract_text_recursive(value, texts)
            elif isinstance(data, list):
                for item in data:
                    extract_text_recursive(item, texts)
            elif data is not None:
                # Convert to string and clean up
                text = str(data).strip()
                if text and len(text) > 1:  # Ignore single characters
                    texts.append(text)
            
            return texts
        
        try:
            text_parts = extract_text_recursive(report_data.content)
            
            # Join with newlines and clean up
            full_text = "\n".join(text_parts)
            
            # Add metadata as context
            metadata_text = f"\nReport ID: {report_data.report_id}\n"
            if report_data.source_url:
                metadata_text += f"Source: {report_data.source_url}\n"
            metadata_text += f"Fetched: {report_data.fetched_at}\n"
            
            return metadata_text + full_text
            
        except Exception as e:
            logger.error(f"Error extracting text from report {report_data.report_id}: {str(e)}")
            return f"Report ID: {report_data.report_id}\nError extracting content: {str(e)}"
