"""API routes for question management."""

import logging
import tempfile
import os
from typing import List
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Request
from fastapi.responses import JSONResponse

from app.models.schemas import (
    QuestionSet, UploadQuestionSetResponse, ErrorResponse
)
from app.services.excel_reader import ExcelQuestionReader
from app.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


def get_vector_store(request: Request):
    """Dependency to get vector store from app state."""
    return request.app.state.vector_store


@router.post("/upload", response_model=UploadQuestionSetResponse)
async def upload_question_set(
    file: UploadFile = File(...),
    vector_store=Depends(get_vector_store)
):
    """
    Upload an Excel file containing questions for validation.
    
    Args:
        file: Excel file containing questions
        
    Returns:
        UploadQuestionSetResponse with parsed questions
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")
        
        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in settings.allowed_excel_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file format. Allowed: {', '.join(settings.allowed_excel_extensions)}"
            )
        
        # Check file size
        content = await file.read()
        if len(content) > settings.max_file_size_mb * 1024 * 1024:
            raise HTTPException(
                status_code=400, 
                detail=f"File too large. Maximum size: {settings.max_file_size_mb}MB"
            )
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Parse questions from Excel
            excel_reader = ExcelQuestionReader()
            question_set = excel_reader.read_questions_from_file(temp_file_path)
            
            # Add questions to vector store for context
            context_name = os.path.splitext(file.filename)[0]
            vector_store.add_question_context(question_set.questions, context_name)
            
            logger.info(f"Successfully uploaded {len(question_set.questions)} questions from {file.filename}")
            
            return UploadQuestionSetResponse(
                message=f"Successfully uploaded {len(question_set.questions)} questions",
                question_set=question_set
            )
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading question set: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to process file: {str(e)}")


@router.get("/validate-file")
async def validate_excel_file(file: UploadFile = File(...)):
    """
    Validate an Excel file structure without uploading.
    
    Args:
        file: Excel file to validate
        
    Returns:
        File validation information
    """
    try:
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")
        
        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in settings.allowed_excel_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file format. Allowed: {', '.join(settings.allowed_excel_extensions)}"
            )
        
        # Save to temporary file
        content = await file.read()
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Validate file structure
            excel_reader = ExcelQuestionReader()
            validation_result = excel_reader.validate_excel_file(temp_file_path)
            
            return validation_result
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating Excel file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to validate file: {str(e)}")


@router.get("/list")
async def list_question_sets(vector_store=Depends(get_vector_store)):
    """
    List all uploaded question sets.
    
    Returns:
        List of question set information
    """
    try:
        # Get collection stats to find question contexts
        stats = vector_store.get_collection_stats()
        
        # Search for question documents
        question_docs = vector_store.collection.get(
            where={"type": "question"},
            include=['metadatas']
        )
        
        # Group by context name
        contexts = {}
        for metadata in question_docs.get('metadatas', []):
            context_name = metadata.get('context_name', 'unknown')
            if context_name not in contexts:
                contexts[context_name] = {
                    'context_name': context_name,
                    'question_count': 0,
                    'categories': set(),
                    'priorities': set()
                }
            
            contexts[context_name]['question_count'] += 1
            
            if metadata.get('category'):
                contexts[context_name]['categories'].add(metadata['category'])
            if metadata.get('priority'):
                contexts[context_name]['priorities'].add(metadata['priority'])
        
        # Convert sets to lists for JSON serialization
        for context in contexts.values():
            context['categories'] = list(context['categories'])
            context['priorities'] = list(context['priorities'])
        
        return {
            "question_sets": list(contexts.values()),
            "total_sets": len(contexts),
            "total_questions": sum(ctx['question_count'] for ctx in contexts.values())
        }
    
    except Exception as e:
        logger.error(f"Error listing question sets: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list question sets: {str(e)}")


@router.delete("/{context_name}")
async def delete_question_set(
    context_name: str,
    vector_store=Depends(get_vector_store)
):
    """
    Delete a question set by context name.
    
    Args:
        context_name: Name of the question set context to delete
        
    Returns:
        Deletion confirmation
    """
    try:
        success = vector_store.delete_question_context(context_name)
        
        if success:
            return {"message": f"Successfully deleted question set: {context_name}"}
        else:
            raise HTTPException(status_code=404, detail=f"Question set not found: {context_name}")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting question set {context_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete question set: {str(e)}")


@router.get("/{context_name}")
async def get_question_set(
    context_name: str,
    vector_store=Depends(get_vector_store)
):
    """
    Get details of a specific question set.
    
    Args:
        context_name: Name of the question set context
        
    Returns:
        Question set details
    """
    try:
        # Get question documents for this context
        results = vector_store.collection.get(
            where={"type": "question", "context_name": context_name},
            include=['documents', 'metadatas']
        )
        
        if not results['ids']:
            raise HTTPException(status_code=404, detail=f"Question set not found: {context_name}")
        
        # Parse questions from results
        questions = []
        for i, doc_id in enumerate(results['ids']):
            metadata = results['metadatas'][i]
            document = results['documents'][i]
            
            # Extract question text from document
            question_text = document.split('\n')[0].replace('Question: ', '')
            
            questions.append({
                'id': metadata.get('question_id', doc_id),
                'text': question_text,
                'category': metadata.get('category', ''),
                'priority': metadata.get('priority', ''),
                'expected_answer_type': metadata.get('expected_answer_type', '')
            })
        
        return {
            "context_name": context_name,
            "questions": questions,
            "total_questions": len(questions)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting question set {context_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get question set: {str(e)}")
