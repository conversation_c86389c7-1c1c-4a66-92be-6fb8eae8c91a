"""Pydantic models and schemas for the AI Report Validator application."""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class ValidationStatus(str, Enum):
    """Status of validation process."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class ValidationResult(str, Enum):
    """Result of validation."""
    PASS = "pass"
    FAIL = "fail"
    PARTIAL = "partial"
    INCONCLUSIVE = "inconclusive"


class Question(BaseModel):
    """Model for a validation question from Excel file."""
    id: str = Field(..., description="Unique identifier for the question")
    text: str = Field(..., description="The question text")
    category: Optional[str] = Field(None, description="Question category")
    priority: Optional[str] = Field(None, description="Question priority level")
    expected_answer_type: Optional[str] = Field(None, description="Expected type of answer")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")


class QuestionSet(BaseModel):
    """Model for a set of questions from an Excel file."""
    filename: str = Field(..., description="Original filename of the Excel file")
    questions: List[Question] = Field(..., description="List of questions")
    uploaded_at: datetime = Field(default_factory=datetime.now, description="Upload timestamp")
    total_questions: int = Field(..., description="Total number of questions")


class ReportData(BaseModel):
    """Model for XML report data."""
    report_id: str = Field(..., description="Unique identifier for the report")
    content: Dict[str, Any] = Field(..., description="Parsed XML content as dictionary")
    source_url: Optional[str] = Field(None, description="Source URL of the report")
    fetched_at: datetime = Field(default_factory=datetime.now, description="Fetch timestamp")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")


class ValidationItem(BaseModel):
    """Model for individual validation result."""
    question_id: str = Field(..., description="ID of the question being validated")
    question_text: str = Field(..., description="Text of the question")
    result: ValidationResult = Field(..., description="Validation result")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence score (0-1)")
    explanation: str = Field(..., description="AI explanation of the validation result")
    relevant_report_sections: List[str] = Field(
        default_factory=list, 
        description="Sections of the report that were relevant to this question"
    )
    processing_time_ms: Optional[int] = Field(None, description="Processing time in milliseconds")


class ValidationReport(BaseModel):
    """Model for complete validation report."""
    validation_id: str = Field(..., description="Unique identifier for this validation")
    report_id: str = Field(..., description="ID of the report being validated")
    question_set_filename: str = Field(..., description="Filename of the question set used")
    status: ValidationStatus = Field(..., description="Overall validation status")
    started_at: datetime = Field(default_factory=datetime.now, description="Validation start time")
    completed_at: Optional[datetime] = Field(None, description="Validation completion time")
    total_questions: int = Field(..., description="Total number of questions validated")
    validation_items: List[ValidationItem] = Field(
        default_factory=list, 
        description="Individual validation results"
    )
    summary: Optional[Dict[str, Any]] = Field(
        default_factory=dict, 
        description="Summary statistics and insights"
    )


# API Request/Response Models

class UploadQuestionSetRequest(BaseModel):
    """Request model for uploading question set."""
    filename: str = Field(..., description="Name of the Excel file")


class UploadQuestionSetResponse(BaseModel):
    """Response model for uploading question set."""
    message: str = Field(..., description="Success message")
    question_set: QuestionSet = Field(..., description="Uploaded question set")


class ValidateReportRequest(BaseModel):
    """Request model for report validation."""
    report_id: str = Field(..., description="ID of the report to validate")
    question_set_filename: str = Field(..., description="Filename of the question set to use")
    external_api_url: Optional[str] = Field(None, description="Override external API URL")


class ValidateReportResponse(BaseModel):
    """Response model for report validation."""
    message: str = Field(..., description="Success message")
    validation_id: str = Field(..., description="ID of the validation process")
    status: ValidationStatus = Field(..., description="Current validation status")


class GetValidationResultResponse(BaseModel):
    """Response model for getting validation results."""
    validation_report: ValidationReport = Field(..., description="Complete validation report")


class ErrorResponse(BaseModel):
    """Standard error response model."""
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")
