"""Service for reading and parsing questions from Excel files."""

import pandas as pd
import uuid
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging

from app.models.schemas import Question, QuestionSet

logger = logging.getLogger(__name__)


class ExcelQuestionReader:
    """Service to read and parse questions from Excel files."""
    
    def __init__(self):
        """Initialize the Excel reader."""
        self.supported_extensions = ['.xlsx', '.xls']
    
    def read_questions_from_file(self, file_path: str, sheet_name: Optional[str] = None) -> QuestionSet:
        """
        Read questions from an Excel file and return a QuestionSet.
        
        Args:
            file_path: Path to the Excel file
            sheet_name: Name of the sheet to read (if None, reads the first sheet)
            
        Returns:
            QuestionSet: Parsed questions from the Excel file
            
        Raises:
            ValueError: If file format is not supported or required columns are missing
            FileNotFoundError: If the file doesn't exist
        """
        file_path_obj = Path(file_path)
        
        if not file_path_obj.exists():
            raise FileNotFoundError(f"Excel file not found: {file_path}")
        
        if file_path_obj.suffix.lower() not in self.supported_extensions:
            raise ValueError(f"Unsupported file format: {file_path_obj.suffix}")
        
        try:
            # Read the Excel file
            df = pd.read_excel(file_path, sheet_name=sheet_name)

            # Validate required columns
            required_columns = ['question', 'text']  # Flexible column naming
            df_columns_lower = [str(col).lower() for col in df.columns]

            # Map common column variations
            column_mapping = self._get_column_mapping(df_columns_lower)
            
            if not column_mapping.get('text'):
                raise ValueError(
                    f"Required column not found. Expected one of: {required_columns}. "
                    f"Found columns: {list(df.columns)}"
                )
            
            # Parse questions
            questions = self._parse_questions_from_dataframe(df, column_mapping)
            
            return QuestionSet(
                filename=file_path_obj.name,
                questions=questions,
                total_questions=len(questions)
            )
            
        except Exception as e:
            logger.error(f"Error reading Excel file {file_path}: {str(e)}")
            raise ValueError(f"Failed to read Excel file: {str(e)}")
    
    def _get_column_mapping(self, columns_lower: List[str]) -> Dict[str, str]:
        """
        Map column names to standard field names.
        
        Args:
            columns_lower: List of column names in lowercase
            
        Returns:
            Dict mapping standard field names to actual column names
        """
        mapping = {}
        
        # Map text/question column
        text_variations = ['question', 'text', 'question_text', 'query', 'prompt']
        for variation in text_variations:
            if variation in columns_lower:
                mapping['text'] = variation
                break
        
        # Map optional columns
        optional_mappings = {
            'id': ['id', 'question_id', 'qid', 'identifier'],
            'category': ['category', 'type', 'section', 'group'],
            'priority': ['priority', 'importance', 'level', 'weight'],
            'expected_answer_type': ['expected_type', 'answer_type', 'response_type', 'format']
        }
        
        for field, variations in optional_mappings.items():
            for variation in variations:
                if variation in columns_lower:
                    mapping[field] = variation
                    break
        
        return mapping
    
    def _parse_questions_from_dataframe(
        self, 
        df: pd.DataFrame, 
        column_mapping: Dict[str, str]
    ) -> List[Question]:
        """
        Parse questions from a pandas DataFrame.
        
        Args:
            df: The DataFrame containing question data
            column_mapping: Mapping of standard field names to actual column names
            
        Returns:
            List of Question objects
        """
        questions = []
        
        for index, row in df.iterrows():
            try:
                # Skip empty rows
                if pd.isna(row[column_mapping['text']]) or str(row[column_mapping['text']]).strip() == '':
                    continue
                
                # Generate ID if not provided
                question_id = str(row[column_mapping.get('id', '')]) if column_mapping.get('id') and not pd.isna(row[column_mapping.get('id', '')]) else str(uuid.uuid4())
                
                # Extract question text
                question_text = str(row[column_mapping['text']]).strip()
                
                # Extract optional fields
                category = self._safe_extract_string(row, column_mapping.get('category'))
                priority = self._safe_extract_string(row, column_mapping.get('priority'))
                expected_answer_type = self._safe_extract_string(row, column_mapping.get('expected_answer_type'))
                
                # Collect additional metadata from other columns
                metadata = {}
                for col in df.columns:
                    col_lower = col.lower()
                    if col_lower not in column_mapping.values() and not pd.isna(row[col]):
                        metadata[col] = str(row[col])
                
                question = Question(
                    id=question_id,
                    text=question_text,
                    category=category,
                    priority=priority,
                    expected_answer_type=expected_answer_type,
                    metadata=metadata if metadata else None
                )
                
                questions.append(question)
                
            except Exception as e:
                logger.warning(f"Error parsing row {index}: {str(e)}")
                continue
        
        if not questions:
            raise ValueError("No valid questions found in the Excel file")
        
        return questions
    
    def _safe_extract_string(self, row: pd.Series, column_name: Optional[str]) -> Optional[str]:
        """
        Safely extract a string value from a pandas Series.
        
        Args:
            row: The pandas Series (row)
            column_name: Name of the column to extract
            
        Returns:
            String value or None if not available
        """
        if not column_name or column_name not in row.index:
            return None
        
        value = row[column_name]
        if pd.isna(value):
            return None
        
        return str(value).strip() if str(value).strip() else None
    
    def validate_excel_file(self, file_path: str) -> Dict[str, Any]:
        """
        Validate an Excel file and return information about its structure.
        
        Args:
            file_path: Path to the Excel file
            
        Returns:
            Dict containing validation information
        """
        try:
            file_path_obj = Path(file_path)
            
            if not file_path_obj.exists():
                return {"valid": False, "error": "File not found"}
            
            if file_path_obj.suffix.lower() not in self.supported_extensions:
                return {"valid": False, "error": f"Unsupported file format: {file_path_obj.suffix}"}
            
            # Read file info
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            # Read first sheet to analyze structure
            df = pd.read_excel(file_path, sheet_name=sheet_names[0])
            
            column_mapping = self._get_column_mapping([str(col).lower() for col in df.columns])
            
            return {
                "valid": True,
                "sheet_names": sheet_names,
                "columns": list(df.columns),
                "total_rows": len(df),
                "detected_question_column": column_mapping.get('text'),
                "column_mapping": column_mapping
            }
            
        except Exception as e:
            return {"valid": False, "error": str(e)}
