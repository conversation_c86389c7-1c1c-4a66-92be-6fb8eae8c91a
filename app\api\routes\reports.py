"""API routes for report management."""

import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends, Request, Query
from fastapi.responses import JSONResponse

from app.models.schemas import ReportData, ErrorResponse
from app.services.report_fetcher import ReportFetcher
from app.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


def get_vector_store(request: Request):
    """Dependency to get vector store from app state."""
    return request.app.state.vector_store


@router.post("/fetch/{report_id}")
async def fetch_report(
    report_id: str,
    endpoint: Optional[str] = Query(None, description="Custom API endpoint"),
    api_url: Optional[str] = Query(None, description="Override base API URL"),
    vector_store=Depends(get_vector_store)
):
    """
    Fetch a report from external API and store it in the vector database.
    
    Args:
        report_id: Unique identifier for the report
        endpoint: Optional custom endpoint path
        api_url: Optional override for base API URL
        
    Returns:
        Report data and storage confirmation
    """
    try:
        if not report_id or not report_id.strip():
            raise HTTPException(status_code=400, detail="Report ID cannot be empty")
        
        # Initialize report fetcher
        fetcher = ReportFetcher(base_url=api_url)
        
        # Fetch the report
        logger.info(f"Fetching report: {report_id}")
        report_data = await fetcher.fetch_report(report_id, endpoint)
        
        # Extract text content for vector storage
        text_content = fetcher.extract_text_content(report_data)
        
        # Store in vector database
        doc_id = vector_store.add_report(report_data, text_content)
        
        logger.info(f"Successfully fetched and stored report {report_id}")
        
        return {
            "message": f"Successfully fetched and stored report {report_id}",
            "report_data": {
                "report_id": report_data.report_id,
                "source_url": report_data.source_url,
                "fetched_at": report_data.fetched_at,
                "content_length": len(text_content),
                "vector_store_id": doc_id
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching report {report_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch report: {str(e)}")


@router.get("/{report_id}")
async def get_report(
    report_id: str,
    vector_store=Depends(get_vector_store)
):
    """
    Get a stored report from the vector database.
    
    Args:
        report_id: Unique identifier for the report
        
    Returns:
        Stored report data
    """
    try:
        if not report_id or not report_id.strip():
            raise HTTPException(status_code=400, detail="Report ID cannot be empty")
        
        # Get report from vector store
        report_doc = vector_store.get_report_content(report_id)
        
        if not report_doc:
            raise HTTPException(status_code=404, detail=f"Report not found: {report_id}")
        
        return {
            "report_id": report_id,
            "document": report_doc['document'],
            "metadata": report_doc['metadata'],
            "vector_store_id": report_doc['id']
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting report {report_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get report: {str(e)}")


@router.delete("/{report_id}")
async def delete_report(
    report_id: str,
    vector_store=Depends(get_vector_store)
):
    """
    Delete a report from the vector database.
    
    Args:
        report_id: Unique identifier for the report
        
    Returns:
        Deletion confirmation
    """
    try:
        if not report_id or not report_id.strip():
            raise HTTPException(status_code=400, detail="Report ID cannot be empty")
        
        success = vector_store.delete_report(report_id)
        
        if success:
            return {"message": f"Successfully deleted report: {report_id}"}
        else:
            raise HTTPException(status_code=404, detail=f"Report not found: {report_id}")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting report {report_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete report: {str(e)}")


@router.get("/")
async def list_reports(vector_store=Depends(get_vector_store)):
    """
    List all stored reports.
    
    Returns:
        List of stored reports with metadata
    """
    try:
        # Get all report documents
        results = vector_store.collection.get(
            where={"type": "report"},
            include=['metadatas']
        )
        
        reports = []
        for i, doc_id in enumerate(results.get('ids', [])):
            metadata = results['metadatas'][i]
            reports.append({
                'report_id': metadata.get('report_id', 'unknown'),
                'source_url': metadata.get('source_url', ''),
                'fetched_at': metadata.get('fetched_at', ''),
                'content_length': metadata.get('content_length', 0),
                'vector_store_id': doc_id
            })
        
        return {
            "reports": reports,
            "total_reports": len(reports)
        }
    
    except Exception as e:
        logger.error(f"Error listing reports: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list reports: {str(e)}")


@router.post("/validate-xml")
async def validate_xml_content(
    xml_content: str,
    report_id: Optional[str] = Query(None, description="Optional report ID for context")
):
    """
    Validate XML content structure without storing it.
    
    Args:
        xml_content: Raw XML content to validate
        report_id: Optional report ID for context
        
    Returns:
        XML validation information
    """
    try:
        if not xml_content or not xml_content.strip():
            raise HTTPException(status_code=400, detail="XML content cannot be empty")
        
        # Initialize report fetcher for validation
        fetcher = ReportFetcher()
        
        # Validate XML structure
        validation_result = fetcher.validate_xml_content(xml_content)
        
        return {
            "report_id": report_id,
            "validation": validation_result,
            "content_preview": xml_content[:500] + "..." if len(xml_content) > 500 else xml_content
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating XML content: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to validate XML: {str(e)}")


@router.post("/search")
async def search_reports(
    query: str,
    max_results: int = Query(5, ge=1, le=20, description="Maximum number of results"),
    vector_store=Depends(get_vector_store)
):
    """
    Search for relevant content across all stored reports.
    
    Args:
        query: Search query text
        max_results: Maximum number of results to return
        
    Returns:
        Relevant report sections matching the query
    """
    try:
        if not query or not query.strip():
            raise HTTPException(status_code=400, detail="Search query cannot be empty")
        
        # Search in vector store
        results = vector_store.search_relevant_content(
            query=query,
            n_results=max_results,
            filter_metadata={"type": "report"}
        )
        
        # Format results
        formatted_results = []
        for result in results:
            metadata = result.get('metadata', {})
            formatted_results.append({
                'report_id': metadata.get('report_id', 'unknown'),
                'content_preview': result['document'][:300] + "..." if len(result['document']) > 300 else result['document'],
                'relevance_score': 1.0 - result.get('distance', 1.0),  # Convert distance to similarity
                'source_url': metadata.get('source_url', ''),
                'fetched_at': metadata.get('fetched_at', '')
            })
        
        return {
            "query": query,
            "results": formatted_results,
            "total_results": len(formatted_results)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error searching reports: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to search reports: {str(e)}")
