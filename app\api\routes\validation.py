"""API routes for report validation."""

import logging
import uuid
import asyncio
from typing import Op<PERSON>, Dict, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks
from fastapi.responses import JSONResponse

from app.models.schemas import (
    ValidateReportRequest, ValidateReportResponse, GetValidationResultResponse,
    ValidationReport, ValidationStatus, ValidationResult, Question
)
from app.services.rag_pipeline import RAGPipeline
from app.services.validation_service import ValidationService

logger = logging.getLogger(__name__)

router = APIRouter()

# In-memory storage for validation results (in production, use a proper database)
validation_results: Dict[str, ValidationReport] = {}


def get_vector_store(request: Request):
    """Dependency to get vector store from app state."""
    return request.app.state.vector_store


@router.post("/start", response_model=ValidateReportResponse)
async def start_validation(
    request: ValidateReportRequest,
    background_tasks: BackgroundTasks,
    vector_store=Depends(get_vector_store)
):
    """
    Start validation of a report against a question set.
    
    Args:
        request: Validation request with report ID and question set
        background_tasks: FastAPI background tasks
        
    Returns:
        Validation ID and status
    """
    try:
        # Generate validation ID
        validation_id = str(uuid.uuid4())
        
        # Check if report exists in vector store
        report_doc = vector_store.get_report_content(request.report_id)
        if not report_doc:
            raise HTTPException(
                status_code=404, 
                detail=f"Report not found: {request.report_id}"
            )
        
        # Get questions from the specified question set
        question_results = vector_store.collection.get(
            where={"type": "question", "context_name": request.question_set_filename},
            include=['documents', 'metadatas']
        )
        
        if not question_results['ids']:
            raise HTTPException(
                status_code=404,
                detail=f"Question set not found: {request.question_set_filename}"
            )
        
        # Parse questions from vector store results
        questions = []
        for i, doc_id in enumerate(question_results['ids']):
            metadata = question_results['metadatas'][i]
            document = question_results['documents'][i]
            
            # Extract question text from document
            question_text = document.split('\n')[0].replace('Question: ', '')
            
            question = Question(
                id=metadata.get('question_id', doc_id),
                text=question_text,
                category=metadata.get('category'),
                priority=metadata.get('priority'),
                expected_answer_type=metadata.get('expected_answer_type')
            )
            questions.append(question)
        
        # Create initial validation report
        validation_report = ValidationReport(
            validation_id=validation_id,
            report_id=request.report_id,
            question_set_filename=request.question_set_filename,
            status=ValidationStatus.PENDING,
            total_questions=len(questions)
        )
        
        # Store initial report
        validation_results[validation_id] = validation_report
        
        # Start validation in background
        background_tasks.add_task(
            perform_validation,
            validation_id,
            questions,
            request.report_id,
            vector_store
        )
        
        logger.info(f"Started validation {validation_id} for report {request.report_id}")
        
        return ValidateReportResponse(
            message=f"Validation started for report {request.report_id}",
            validation_id=validation_id,
            status=ValidationStatus.PENDING
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting validation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start validation: {str(e)}")


async def perform_validation(
    validation_id: str,
    questions: list[Question],
    report_id: str,
    vector_store
):
    """
    Perform the actual validation in the background.
    
    Args:
        validation_id: ID of the validation process
        questions: List of questions to validate
        report_id: ID of the report to validate against
        vector_store: Vector store instance
    """
    try:
        # Update status to in progress
        if validation_id in validation_results:
            validation_results[validation_id].status = ValidationStatus.IN_PROGRESS
        
        # Initialize RAG pipeline
        rag_pipeline = RAGPipeline(vector_store)
        
        # Perform validation for all questions
        validation_items = await rag_pipeline.validate_multiple_questions(questions, report_id)
        
        # Calculate summary statistics
        summary = calculate_validation_summary(validation_items)
        
        # Update validation report
        if validation_id in validation_results:
            validation_report = validation_results[validation_id]
            validation_report.status = ValidationStatus.COMPLETED
            validation_report.completed_at = datetime.now()
            validation_report.validation_items = validation_items
            validation_report.summary = summary
        
        logger.info(f"Completed validation {validation_id}")
        
    except Exception as e:
        logger.error(f"Error performing validation {validation_id}: {str(e)}")
        
        # Update status to failed
        if validation_id in validation_results:
            validation_results[validation_id].status = ValidationStatus.FAILED
            validation_results[validation_id].summary = {"error": str(e)}


def calculate_validation_summary(validation_items) -> Dict[str, Any]:
    """Calculate summary statistics for validation results."""
    total = len(validation_items)
    if total == 0:
        return {"total": 0}
    
    # Count results by type
    result_counts = {}
    confidence_scores = []
    processing_times = []
    
    for item in validation_items:
        result_type = item.result.value
        result_counts[result_type] = result_counts.get(result_type, 0) + 1
        confidence_scores.append(item.confidence_score)
        if item.processing_time_ms:
            processing_times.append(item.processing_time_ms)
    
    # Calculate percentages
    result_percentages = {
        result: (count / total) * 100 
        for result, count in result_counts.items()
    }
    
    # Calculate average confidence and processing time
    avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
    avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
    
    return {
        "total_questions": total,
        "result_counts": result_counts,
        "result_percentages": result_percentages,
        "average_confidence": round(avg_confidence, 3),
        "average_processing_time_ms": round(avg_processing_time, 2),
        "pass_rate": result_percentages.get("pass", 0),
        "fail_rate": result_percentages.get("fail", 0)
    }


@router.get("/{validation_id}", response_model=GetValidationResultResponse)
async def get_validation_result(validation_id: str):
    """
    Get validation results by validation ID.
    
    Args:
        validation_id: ID of the validation process
        
    Returns:
        Complete validation report
    """
    try:
        if validation_id not in validation_results:
            raise HTTPException(status_code=404, detail=f"Validation not found: {validation_id}")
        
        validation_report = validation_results[validation_id]
        
        return GetValidationResultResponse(validation_report=validation_report)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting validation result {validation_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get validation result: {str(e)}")


@router.get("/{validation_id}/status")
async def get_validation_status(validation_id: str):
    """
    Get validation status by validation ID.
    
    Args:
        validation_id: ID of the validation process
        
    Returns:
        Current validation status and basic info
    """
    try:
        if validation_id not in validation_results:
            raise HTTPException(status_code=404, detail=f"Validation not found: {validation_id}")
        
        validation_report = validation_results[validation_id]
        
        return {
            "validation_id": validation_id,
            "status": validation_report.status,
            "report_id": validation_report.report_id,
            "question_set_filename": validation_report.question_set_filename,
            "total_questions": validation_report.total_questions,
            "completed_questions": len(validation_report.validation_items),
            "started_at": validation_report.started_at,
            "completed_at": validation_report.completed_at
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting validation status {validation_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get validation status: {str(e)}")


@router.get("/")
async def list_validations():
    """
    List all validation processes.
    
    Returns:
        List of all validations with basic information
    """
    try:
        validations = []
        for validation_id, report in validation_results.items():
            validations.append({
                "validation_id": validation_id,
                "status": report.status,
                "report_id": report.report_id,
                "question_set_filename": report.question_set_filename,
                "total_questions": report.total_questions,
                "completed_questions": len(report.validation_items),
                "started_at": report.started_at,
                "completed_at": report.completed_at
            })
        
        return {
            "validations": validations,
            "total_validations": len(validations)
        }
    
    except Exception as e:
        logger.error(f"Error listing validations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list validations: {str(e)}")


@router.delete("/{validation_id}")
async def delete_validation(validation_id: str):
    """
    Delete a validation result.
    
    Args:
        validation_id: ID of the validation to delete
        
    Returns:
        Deletion confirmation
    """
    try:
        if validation_id not in validation_results:
            raise HTTPException(status_code=404, detail=f"Validation not found: {validation_id}")
        
        del validation_results[validation_id]
        
        return {"message": f"Successfully deleted validation: {validation_id}"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting validation {validation_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete validation: {str(e)}")
