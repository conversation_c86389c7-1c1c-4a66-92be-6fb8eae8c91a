"""Validation service for coordinating report validation workflows."""

import logging
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime

from app.models.schemas import (
    Question, ValidationReport, ValidationItem, ValidationStatus, ValidationResult
)
from app.services.rag_pipeline import RAGPipeline
from app.services.vector_store import VectorStore

logger = logging.getLogger(__name__)


class ValidationService:
    """Service for coordinating report validation workflows."""
    
    def __init__(self, vector_store: VectorStore):
        """
        Initialize the validation service.
        
        Args:
            vector_store: Vector store instance for data access
        """
        self.vector_store = vector_store
        self.rag_pipeline = RAGPipeline(vector_store)
    
    async def create_validation_job(
        self, 
        report_id: str, 
        question_set_name: str
    ) -> ValidationReport:
        """
        Create a new validation job.
        
        Args:
            report_id: ID of the report to validate
            question_set_name: Name of the question set to use
            
        Returns:
            Initial validation report
            
        Raises:
            ValueError: If report or question set not found
        """
        # Validate report exists
        report_doc = self.vector_store.get_report_content(report_id)
        if not report_doc:
            raise ValueError(f"Report not found: {report_id}")
        
        # Get questions from vector store
        questions = self._get_questions_from_vector_store(question_set_name)
        if not questions:
            raise ValueError(f"Question set not found: {question_set_name}")
        
        # Create validation report
        validation_id = str(uuid.uuid4())
        validation_report = ValidationReport(
            validation_id=validation_id,
            report_id=report_id,
            question_set_filename=question_set_name,
            status=ValidationStatus.PENDING,
            total_questions=len(questions)
        )
        
        logger.info(f"Created validation job {validation_id} for report {report_id}")
        return validation_report
    
    async def execute_validation(
        self, 
        validation_report: ValidationReport
    ) -> ValidationReport:
        """
        Execute the validation process.
        
        Args:
            validation_report: The validation report to execute
            
        Returns:
            Updated validation report with results
        """
        try:
            # Update status
            validation_report.status = ValidationStatus.IN_PROGRESS
            
            # Get questions
            questions = self._get_questions_from_vector_store(
                validation_report.question_set_filename
            )
            
            # Perform validation
            validation_items = await self.rag_pipeline.validate_multiple_questions(
                questions, validation_report.report_id
            )
            
            # Calculate summary
            summary = self._calculate_summary(validation_items)
            
            # Update report
            validation_report.status = ValidationStatus.COMPLETED
            validation_report.completed_at = datetime.now()
            validation_report.validation_items = validation_items
            validation_report.summary = summary
            
            logger.info(f"Completed validation {validation_report.validation_id}")
            
        except Exception as e:
            logger.error(f"Validation failed for {validation_report.validation_id}: {str(e)}")
            validation_report.status = ValidationStatus.FAILED
            validation_report.summary = {"error": str(e)}
        
        return validation_report
    
    def _get_questions_from_vector_store(self, question_set_name: str) -> List[Question]:
        """Get questions from vector store by question set name."""
        try:
            results = self.vector_store.collection.get(
                where={"type": "question", "context_name": question_set_name},
                include=['documents', 'metadatas']
            )
            
            questions = []
            for i, doc_id in enumerate(results.get('ids', [])):
                metadata = results['metadatas'][i]
                document = results['documents'][i]
                
                # Extract question text from document
                question_text = document.split('\n')[0].replace('Question: ', '')
                
                question = Question(
                    id=metadata.get('question_id', doc_id),
                    text=question_text,
                    category=metadata.get('category'),
                    priority=metadata.get('priority'),
                    expected_answer_type=metadata.get('expected_answer_type')
                )
                questions.append(question)
            
            return questions
            
        except Exception as e:
            logger.error(f"Error getting questions for {question_set_name}: {str(e)}")
            return []
    
    def _calculate_summary(self, validation_items: List[ValidationItem]) -> Dict[str, Any]:
        """Calculate summary statistics for validation results."""
        total = len(validation_items)
        if total == 0:
            return {"total": 0}
        
        # Count results by type
        result_counts = {}
        confidence_scores = []
        processing_times = []
        
        for item in validation_items:
            result_type = item.result.value
            result_counts[result_type] = result_counts.get(result_type, 0) + 1
            confidence_scores.append(item.confidence_score)
            if item.processing_time_ms:
                processing_times.append(item.processing_time_ms)
        
        # Calculate percentages
        result_percentages = {
            result: (count / total) * 100 
            for result, count in result_counts.items()
        }
        
        # Calculate averages
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        # Identify high/low confidence items
        high_confidence_items = [item for item in validation_items if item.confidence_score >= 0.8]
        low_confidence_items = [item for item in validation_items if item.confidence_score < 0.5]
        
        return {
            "total_questions": total,
            "result_counts": result_counts,
            "result_percentages": result_percentages,
            "average_confidence": round(avg_confidence, 3),
            "average_processing_time_ms": round(avg_processing_time, 2),
            "pass_rate": result_percentages.get("pass", 0),
            "fail_rate": result_percentages.get("fail", 0),
            "high_confidence_count": len(high_confidence_items),
            "low_confidence_count": len(low_confidence_items),
            "completion_time": datetime.now().isoformat()
        }
    
    def get_validation_insights(self, validation_report: ValidationReport) -> Dict[str, Any]:
        """
        Generate insights from validation results.
        
        Args:
            validation_report: Completed validation report
            
        Returns:
            Dict containing validation insights
        """
        if validation_report.status != ValidationStatus.COMPLETED:
            return {"error": "Validation not completed"}
        
        items = validation_report.validation_items
        if not items:
            return {"error": "No validation items found"}
        
        # Analyze by category
        category_analysis = {}
        for item in items:
            # Try to get category from question metadata or use 'general'
            category = "general"  # Default category
            
            if category not in category_analysis:
                category_analysis[category] = {
                    "total": 0,
                    "pass": 0,
                    "fail": 0,
                    "partial": 0,
                    "inconclusive": 0,
                    "avg_confidence": 0
                }
            
            category_analysis[category]["total"] += 1
            category_analysis[category][item.result.value] += 1
        
        # Calculate category averages
        for category, data in category_analysis.items():
            category_items = [item for item in items]  # All items for now
            if category_items:
                data["avg_confidence"] = sum(item.confidence_score for item in category_items) / len(category_items)
        
        # Find problematic areas
        failed_items = [item for item in items if item.result == ValidationResult.FAIL]
        low_confidence_items = [item for item in items if item.confidence_score < 0.5]
        
        return {
            "category_analysis": category_analysis,
            "problematic_questions": [
                {
                    "question_id": item.question_id,
                    "question_text": item.question_text[:100] + "..." if len(item.question_text) > 100 else item.question_text,
                    "result": item.result.value,
                    "confidence": item.confidence_score,
                    "explanation": item.explanation[:200] + "..." if len(item.explanation) > 200 else item.explanation
                }
                for item in failed_items[:5]  # Top 5 failed items
            ],
            "low_confidence_questions": [
                {
                    "question_id": item.question_id,
                    "question_text": item.question_text[:100] + "..." if len(item.question_text) > 100 else item.question_text,
                    "confidence": item.confidence_score,
                    "explanation": item.explanation[:200] + "..." if len(item.explanation) > 200 else item.explanation
                }
                for item in low_confidence_items[:5]  # Top 5 low confidence items
            ],
            "recommendations": self._generate_recommendations(validation_report)
        }
    
    def _generate_recommendations(self, validation_report: ValidationReport) -> List[str]:
        """Generate recommendations based on validation results."""
        recommendations = []
        
        if not validation_report.summary:
            return ["Complete validation analysis to generate recommendations"]
        
        summary = validation_report.summary
        
        # Check pass rate
        pass_rate = summary.get("pass_rate", 0)
        if pass_rate < 50:
            recommendations.append("Low pass rate detected. Consider reviewing report completeness and question relevance.")
        
        # Check confidence levels
        avg_confidence = summary.get("average_confidence", 0)
        if avg_confidence < 0.6:
            recommendations.append("Low average confidence. Consider improving question clarity or report detail.")
        
        # Check for high failure rate
        fail_rate = summary.get("fail_rate", 0)
        if fail_rate > 30:
            recommendations.append("High failure rate. Review failed questions for common patterns.")
        
        # Check processing time
        avg_time = summary.get("average_processing_time_ms", 0)
        if avg_time > 5000:  # 5 seconds
            recommendations.append("High processing time detected. Consider optimizing question complexity.")
        
        if not recommendations:
            recommendations.append("Validation completed successfully with good results.")
        
        return recommendations
